import pandas as pd
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn  
import os

def is_valid_value(value):
    """判断值是否有效：非空且不等于0/0.0"""
    if is_empty(value):  
        return False
    try:
        num = round(float(value), 2)
        return num != 0  
    except (ValueError, TypeError):
        return True

def format_decimal(value):
    """格式化数值，保留2位小数"""
    if is_empty(value):
        return ""
    try:
        return f"{round(float(value), 2):.2f}"
    except (ValueError, TypeError):
        return str(value).strip()    

def is_empty(value):
    """判断单元格内容是否为空"""
    if pd.isna(value):
        return True
    if isinstance(value, str) and str(value).strip() == "":
        return True
    return False

def delete_existing_file(file_path):
    """删除已存在的文件"""
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            print(f"已删除现有文件：{file_path}")
        except Exception as e:
            print(f"删除文件时出错：{e}")
            raise

def read_hospital_info(excel_path):
    """从表1读取医院基本信息"""
    df = pd.read_excel(excel_path, sheet_name="表1", header=None)
    
    # 从第2行获取医院信息
    if len(df) > 1:
        hospital_name = str(df.iloc[1, 0]).strip() if not is_empty(df.iloc[1, 0]) else "未知医院"
        org_code = str(df.iloc[1, 1]).strip() if not is_empty(df.iloc[1, 1]) else ""
        medical_code = str(df.iloc[1, 2]).strip() if not is_empty(df.iloc[1, 2]) else ""
        category = str(df.iloc[1, 3]).strip() if not is_empty(df.iloc[1, 3]) else ""
        nature = str(df.iloc[1, 4]).strip() if not is_empty(df.iloc[1, 4]) else ""
        level = str(df.iloc[1, 5]).strip() if not is_empty(df.iloc[1, 5]) else ""
        grade = str(df.iloc[1, 6]).strip() if not is_empty(df.iloc[1, 6]) else ""
        legal_person = str(df.iloc[1, 7]).strip() if not is_empty(df.iloc[1, 7]) else ""
        phone = str(df.iloc[1, 8]).strip() if not is_empty(df.iloc[1, 8]) else ""
        check_time = str(df.iloc[1, 9]).strip() if not is_empty(df.iloc[1, 9]) else ""
        check_location = str(df.iloc[1, 10]).strip() if not is_empty(df.iloc[1, 10]) else ""
        data_time = str(df.iloc[1, 11]).strip() if not is_empty(df.iloc[1, 11]) else ""
        medical_income = str(df.iloc[1, 12]).strip() if not is_empty(df.iloc[1, 12]) else ""
        insurance_income = str(df.iloc[1, 13]).strip() if not is_empty(df.iloc[1, 13]) else ""
        reimbursement_rate = str(df.iloc[1, 14]).strip() if not is_empty(df.iloc[1, 14]) else ""
        self_check_amount = str(df.iloc[1, 15]).strip() if not is_empty(df.iloc[1, 15]) else ""
        self_check_return = str(df.iloc[1, 16]).strip() if not is_empty(df.iloc[1, 16]) else ""
        team_leader = str(df.iloc[1, 17]).strip() if not is_empty(df.iloc[1, 17]) else ""
        deputy_leader = str(df.iloc[1, 18]).strip() if not is_empty(df.iloc[1, 18]) else ""
        team_members = str(df.iloc[1, 19]).strip() if not is_empty(df.iloc[1, 19]) else ""
        
        return {
            'hospital_name': hospital_name,
            'org_code': org_code,
            'medical_code': medical_code,
            'category': category,
            'nature': nature,
            'level': level,
            'grade': grade,
            'legal_person': legal_person,
            'phone': phone,
            'check_time': check_time,
            'check_location': check_location,
            'data_time': data_time,
            'medical_income': medical_income,
            'insurance_income': insurance_income,
            'reimbursement_rate': reimbursement_rate,
            'self_check_amount': self_check_amount,
            'self_check_return': self_check_return,
            'team_leader': team_leader,
            'deputy_leader': deputy_leader,
            'team_members': team_members
        }
    else:
        return None

def read_problem_data(excel_path):
    """从表2读取问题数据"""
    df = pd.read_excel(excel_path, sheet_name="表2", header=0)
    return df

def calculate_totals(df):
    """计算总计数据"""
    total_quantity = df['问题数量'].sum()
    total_violation_amount = df['违规医药费用总额（元）'].sum()
    total_fund_amount = df['违规使用医保基金（元）'].sum()
    
    return {
        'total_quantity': total_quantity,
        'total_violation_amount': total_violation_amount,
        'total_fund_amount': total_fund_amount
    }

def set_paragraph_font(paragraph, font_name=None, font_size=16, bold=None, alignment=None, first_line_indent=32):
    """设置段落的字体格式，如果font_name为None则不修改字体"""
    for run in paragraph.runs:
        if font_name is not None:
            run.font.name = font_name
            run.font.element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
        run.font.size = Pt(font_size)
        if bold is not None:
            run.font.bold = bold

    if alignment is not None:
        paragraph.paragraph_format.alignment = alignment
    if first_line_indent is not None:
        paragraph.paragraph_format.first_line_indent = Pt(first_line_indent)

def setup_document_styles(doc):
    """设置文档样式，完全按照附件7-柳州市柳江区人民医院.docx的样式"""

    # 主标题样式 (飞行检查报告)
    title1_style = doc.styles['Heading 1']
    title1_style.font.name = 'Times New Roman'
    title1_style.font.size = Pt(22)
    title1_style.font.bold = False
    title1_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    title1_style.font.element.rPr.rFonts.set(qn('w:eastAsia'), 'Times New Roman')
    title1_style.font.color.rgb = RGBColor(0, 0, 0)
    title1_style.paragraph_format.space_before = None
    title1_style.paragraph_format.space_after = None
    title1_style.paragraph_format.first_line_indent = None

    # 二级标题样式 (检查总体情况、违法违规使用医保基金的问题等)
    title2_style = doc.styles['Heading 2']
    title2_style.font.name = '黑体'
    title2_style.font.size = Pt(16)
    title2_style.font.bold = False
    title2_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    title2_style.font.element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
    title2_style.font.color.rgb = RGBColor(0, 0, 0)
    title2_style.paragraph_format.space_before = None
    title2_style.paragraph_format.space_after = None
    title2_style.paragraph_format.first_line_indent = Pt(32)

    # 三级标题样式 (一般违规问题、违反诊疗规范过度诊疗等)
    title3_style = doc.styles['Heading 3']
    title3_style.font.name = '楷体_GB2312'
    title3_style.font.size = Pt(16)
    title3_style.font.bold = True
    title3_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    title3_style.font.element.rPr.rFonts.set(qn('w:eastAsia'), '楷体_GB2312')
    title3_style.font.color.rgb = RGBColor(0, 0, 0)
    title3_style.paragraph_format.space_before = None
    title3_style.paragraph_format.space_after = None
    title3_style.paragraph_format.first_line_indent = Pt(32)

    # 正文样式
    normal_style = doc.styles['Normal']
    normal_style.font.name = 'FangSong_GB2312'
    normal_style.font.size = Pt(16)
    normal_style.font.bold = False
    normal_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    normal_style.font.element.rPr.rFonts.set(qn('w:eastAsia'), 'FangSong_GB2312')
    normal_style.font.color.rgb = RGBColor(0, 0, 0)
    normal_style.paragraph_format.space_before = None
    normal_style.paragraph_format.space_after = None
    normal_style.paragraph_format.first_line_indent = Pt(32)

def create_report(excel_path, output_path):
    """创建飞行检查报告"""
    delete_existing_file(output_path)

    # 读取数据
    hospital_info = read_hospital_info(excel_path)
    problem_df = read_problem_data(excel_path)
    totals = calculate_totals(problem_df)

    if not hospital_info:
        raise ValueError("无法读取医院基本信息")

    # 创建文档
    doc = Document()
    setup_document_styles(doc)

    # 1. 添加标题
    title_para = doc.add_heading("飞行检查报告", level=1)
    set_paragraph_font(title_para, font_name=None, font_size=22, alignment=WD_PARAGRAPH_ALIGNMENT.CENTER, first_line_indent=None)

    # 2. 添加医院基本信息
    info_paras = [
        doc.add_paragraph(f"被检单位名称：{hospital_info['hospital_name']}"),
        doc.add_paragraph(f"被检单位机构代码：{hospital_info['org_code']}"),
        doc.add_paragraph(f"被检单位医保机构编码：{hospital_info['medical_code']}"),
        doc.add_paragraph(f"被检单位类别/性质/等级：{hospital_info['category']}/{hospital_info['nature']}/{hospital_info['level']}{hospital_info['grade']}"),
        doc.add_paragraph(f"被检单位法定代表人：{hospital_info['legal_person']}"),
        doc.add_paragraph(f"被检单位法定代表人联系电话：{hospital_info['phone']}"),
        doc.add_paragraph(f"检查时间：{hospital_info['check_time']}"),
        doc.add_paragraph(f"检查地点：{hospital_info['check_location']}"),
        doc.add_paragraph(f"飞行检查组组长：{hospital_info['team_leader']}"),
        doc.add_paragraph(f"副组长：{hospital_info['deputy_leader']}"),
        doc.add_paragraph(f"检查组成员：{hospital_info['team_members']}")
    ]

    # 设置医院基本信息的字体格式
    for para in info_paras:
        set_paragraph_font(para, font_name='Times New Roman')

    # 3. 检查总体情况
    situation_heading = doc.add_heading("一、检查总体情况", level=2)
    set_paragraph_font(situation_heading, font_name=None)  # 使用样式中的字体

    # 添加医疗收入和医保基金收入信息
    total_para = doc.add_paragraph()
    total_para.add_run(f"该院医疗总收入：{hospital_info['medical_income']}。医保基金收入（含基本医保、大病保险、医疗救助、补充医保等）：{hospital_info['insurance_income']}。报销比例：{hospital_info['reimbursement_rate']}。")
    total_para.paragraph_format.line_spacing = 1.5
    set_paragraph_font(total_para, font_name='Times New Roman')

    # 4. 违法违规使用医保基金的问题
    violation_heading = doc.add_heading("二、违法违规使用医保基金的问题", level=2)
    set_paragraph_font(violation_heading, font_name=None)  # 使用样式中的字体

    # 按问题类别分组处理
    grouped_level1 = problem_df.groupby('问题类别(一级指标)')
    level1_counter = 1

    for level1_name, level1_group in grouped_level1:
        # 添加一级标题（使用中文序号）
        chinese_numbers = ['（一）', '（二）', '（三）', '（四）', '（五）']
        if level1_counter <= len(chinese_numbers):
            numbered_level1_title = f"{chinese_numbers[level1_counter-1]}{level1_name}"
        else:
            numbered_level1_title = f"（{level1_counter}）{level1_name}"

        level1_heading = doc.add_heading(numbered_level1_title, level=3)
        set_paragraph_font(level1_heading, font_name=None, bold=True)  # 使用样式中的字体
        level1_counter += 1

        # 按二级指标分组
        grouped_level2 = level1_group.groupby('问题类别(二级指标)')
        level2_counter = 1

        for level2_name, level2_group in grouped_level2:
            # 添加二级标题（如果不为空）
            if not is_empty(level2_name) and str(level2_name).strip() != 'nan':
                # 使用阿拉伯数字序号
                numbered_level2_title = f"{level2_counter}. {level2_name}"
                level2_heading = doc.add_heading(numbered_level2_title, level=3)
                set_paragraph_font(level2_heading, font_name=None, bold=True)  # 使用样式中的字体
                level2_counter += 1

            # 处理每个问题项目
            item_counter = 1
            for _, row in level2_group.iterrows():
                # 将所有信息合并到一个段落中
                content_parts = []

                # 问题项目名称和编码（添加序号）
                if not is_empty(row['问题项目']):
                    content_parts.append(f"({item_counter}){row['问题项目']}（编码：{row['问题项目编码']}）。")
                    item_counter += 1

                # 认定依据
                if not is_empty(row['认定依据']):
                    content_parts.append(f"{row['认定依据']}")

                # 问题情形描述
                if not is_empty(row['问题情形描述']):
                    content_parts.append(f"{row['问题情形描述']}")

                # 涉及金额信息
                quantity = format_decimal(row['问题数量'])
                violation_amount = format_decimal(row['违规医药费用总额（元）'])
                fund_amount = format_decimal(row['违规使用医保基金（元）'])

                amount_parts = []
                if is_valid_value(quantity):
                    amount_parts.append(f"涉及{quantity}人次")
                if is_valid_value(fund_amount):
                    amount_parts.append(f"涉及医保基金{fund_amount}元")

                if amount_parts:
                    content_parts.append(f"2023年—2025年7月，{', '.join(amount_parts)}。")

                # 创建一个完整的段落
                if content_parts:
                    item_para = doc.add_paragraph()
                    item_para.add_run(" ".join(content_parts))
                    item_para.paragraph_format.line_spacing = 1.5
                    set_paragraph_font(item_para, font_name='Times New Roman', bold=True)  # 问题项目段落使用Times New Roman加粗
    
    # 5. 检查发现涉及其他部门职能的相关问题
    other_dept_heading = doc.add_heading("三、检查发现涉及其他部门职能的相关问题", level=2)
    set_paragraph_font(other_dept_heading, font_name=None)  # 使用样式中的字体

    # 涉及卫生健康部门职能问题
    health_dept_heading = doc.add_heading("涉及卫生健康部门职能问题", level=3)
    set_paragraph_font(health_dept_heading, font_name=None, bold=True)  # 使用样式中的字体

    # 从问题数据中筛选需要移交卫健部门的问题
    health_dept_problems = problem_df[problem_df['处理建议-移交卫健'] == '移交']
    if not health_dept_problems.empty:
        health_issues = []
        for _, row in health_dept_problems.iterrows():
            if not is_empty(row['问题项目']) and not is_empty(row['问题类别(二级指标)']):
                health_issues.append(f"{row['问题项目']}等{row['问题类别(二级指标)']}问题")

        if health_issues:
            # 去重并编号
            unique_issues = list(set(health_issues))
            for i, issue in enumerate(unique_issues[:3], 1):  # 最多显示3个
                para = doc.add_paragraph()
                para.add_run(f"{i}.{issue}。")
                para.paragraph_format.line_spacing = 1.5
                set_paragraph_font(para, font_name='Times New Roman', bold=False)  # 序号段落不加粗

    # 涉及药品监管部门职能问题
    drug_dept_heading = doc.add_heading("涉及药品监管部门职能问题", level=3)
    set_paragraph_font(drug_dept_heading, font_name=None, bold=True)  # 使用样式中的字体

    # 从问题数据中筛选需要移交药监部门的问题
    drug_dept_problems = problem_df[problem_df['处理建议-移交药监'] == '移交']
    if not drug_dept_problems.empty:
        drug_issues = []
        for _, row in drug_dept_problems.iterrows():
            if not is_empty(row['问题项目']) and not is_empty(row['问题类别(二级指标)']):
                drug_issues.append(f"{row['问题项目']}等{row['问题类别(二级指标)']}问题")

        if drug_issues:
            # 去重并编号
            unique_issues = list(set(drug_issues))
            for i, issue in enumerate(unique_issues[:3], 1):  # 最多显示3个
                para = doc.add_paragraph()
                para.add_run(f"{i}.{issue}。")
                para.paragraph_format.line_spacing = 1.5
                set_paragraph_font(para, font_name='Times New Roman', bold=False)  # 序号段落不加粗

    # 6. 飞行检查组意见
    opinion_heading = doc.add_heading("四、飞行检查组意见", level=2)
    set_paragraph_font(opinion_heading, font_name=None)  # 使用样式中的字体

    opinion_paragraphs = [
        "按照《社会保险法》《医疗保障基金使用监督管理条例》《医疗保障基金飞行检查管理暂行办法》等法律法规规定，完成飞行检查后续处理工作。",
        "（一）做好后续处置。对反馈报告指出的问题，依法依规完成后续处置。对参与其中的相关人员，按照《国家医保局 国家卫生健康委 国家药监局关于建立定点医药机构相关人员医保支付资格管理制度的指导意见》进行支付资格管理。",
        "（二）做好举一反三。一是对飞行检查组已指出问题，但受时间限制，暂未对所有患者就医情况开展全面检查并确认违法违规使用医保基金的，进一步开展延伸检查、依法依规做好后续处置。二是对飞行检查指出属于区域性、普遍性的问题，举一反三，在全区范围内开展专项整治。",
        "（三）做好问题移送。将违反诊疗规范过度诊疗、过度检查、超标准收费等问题，移交卫生健康部门处理。将将不属于医疗保障基金支付范围的医药费用纳入医疗保障基金结算等问题，移交药品监管部门处理。"
    ]

    for para_text in opinion_paragraphs:
        para = doc.add_paragraph()
        para.add_run(para_text)
        para.paragraph_format.line_spacing = 1.5
        set_paragraph_font(para, font_name='Times New Roman', bold=False)  # 意见段落不加粗

    # 7. 签名部分
    signature_paras = [
        doc.add_paragraph("\n\n"),
        doc.add_paragraph("飞行检查组组长签名："),
        doc.add_paragraph("\n"),
        doc.add_paragraph("接收单位（代表）签名："),
        doc.add_paragraph("\n"),
        doc.add_paragraph("年  月  日"),
        doc.add_paragraph("\n\n"),
        doc.add_paragraph("（本文书一式二份，一份移交属地医疗保障行政部门，一份上交组织飞行检查的医疗保障行政部门归档）")
    ]

    for para in signature_paras:
        if para.text.strip():  # 只对非空段落设置字体
            set_paragraph_font(para, font_name='Times New Roman')
    
    # 保存文档
    doc.save(output_path)
    print(f"报告已生成：{output_path}")
    
    # 打印总计信息
    print(f"\n总计信息：")
    print(f"问题总数量：{totals['total_quantity']}")
    print(f"违规医药费用总额：{format_decimal(totals['total_violation_amount'])}元")
    print(f"违规使用医保基金：{format_decimal(totals['total_fund_amount'])}元")

def main():
    """主函数"""
    import sys
    import argparse

    # 设置命令行参数
    parser = argparse.ArgumentParser(description='从Excel模板生成飞行检查Word报告')
    parser.add_argument('--excel', '-e',
                       default="附件7-1.飞行检查情况反馈报告-医疗机构模板.xlsx",
                       help='Excel模板文件路径')
    parser.add_argument('--output', '-o',
                       default="生成的飞行检查报告.docx",
                       help='输出Word文档路径')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.excel):
        print(f"错误：Excel文件不存在：{args.excel}")
        sys.exit(1)

    try:
        print(f"正在读取Excel文件：{args.excel}")
        create_report(args.excel, args.output)
        print(f"✓ 报告生成成功：{args.output}")
    except FileNotFoundError as e:
        print(f"错误：文件未找到：{e}")
        sys.exit(1)
    except pd.errors.EmptyDataError:
        print("错误：Excel文件为空或格式不正确")
        sys.exit(1)
    except KeyError as e:
        print(f"错误：Excel文件缺少必要的列：{e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误：生成报告时出错：{e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
