from docx import Document

# 验证字体设置
doc_path = "正确字体的飞行检查报告.docx"

try:
    doc = Document(doc_path)
    
    print("字体验证:")
    print("=" * 60)
    
    # 查找不同类型的段落并验证字体
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        
        if not text:
            continue
            
        # 主标题
        if text == "飞行检查报告":
            print(f"\n主标题 {i+1}: {text}")
            if para.runs:
                run = para.runs[0]
                font_size = run.font.size.pt if run.font.size and hasattr(run.font.size, 'pt') else 0
                print(f"  字体: {run.font.name}, 大小: {font_size:.1f}磅, 加粗: {run.font.bold}")
                print(f"  样式: {para.style.name}")
                print(f"  期望字体: Times New Roman")
        
        # 二级标题（一、二、三、四）
        elif text.startswith(('一、', '二、', '三、', '四、')):
            print(f"\n二级标题 {i+1}: {text}")
            if para.runs:
                run = para.runs[0]
                font_size = run.font.size.pt if run.font.size and hasattr(run.font.size, 'pt') else 0
                print(f"  字体: {run.font.name}, 大小: {font_size:.1f}磅, 加粗: {run.font.bold}")
                print(f"  样式: {para.style.name}")
                print(f"  期望字体: 黑体")
        
        # 三级标题（（一）（二）或1. 2.）
        elif (text.startswith(('（一）', '（二）', '（三）')) or 
              text.startswith(('1. ', '2. ', '3. ', '4. ', '5. '))):
            print(f"\n三级标题 {i+1}: {text[:50]}{'...' if len(text) > 50 else ''}")
            if para.runs:
                run = para.runs[0]
                font_size = run.font.size.pt if run.font.size and hasattr(run.font.size, 'pt') else 0
                print(f"  字体: {run.font.name}, 大小: {font_size:.1f}磅, 加粗: {run.font.bold}")
                print(f"  样式: {para.style.name}")
                print(f"  期望字体: 楷体_GB2312")
        
        # 问题项目（(1) (2) (3)）
        elif text.startswith(('(1)', '(2)', '(3)', '(4)', '(5)')):
            print(f"\n问题项目 {i+1}: {text[:50]}{'...' if len(text) > 50 else ''}")
            if para.runs:
                run = para.runs[0]
                font_size = run.font.size.pt if run.font.size and hasattr(run.font.size, 'pt') else 0
                print(f"  字体: {run.font.name}, 大小: {font_size:.1f}磅, 加粗: {run.font.bold}")
                print(f"  样式: {para.style.name}")
                print(f"  期望字体: Times New Roman")
            break  # 只显示第一个问题项目
    
    print("\n" + "=" * 60)
    print("字体设置总结:")
    
    # 统计不同字体的使用情况
    font_usage = {}
    for para in doc.paragraphs:
        if para.runs:
            font_name = para.runs[0].font.name
            if font_name:
                font_usage[font_name] = font_usage.get(font_name, 0) + 1
    
    print("文档中使用的字体:")
    for font, count in font_usage.items():
        print(f"  {font}: {count} 个段落")
        
except Exception as e:
    print(f"验证文档时出错: {e}")
