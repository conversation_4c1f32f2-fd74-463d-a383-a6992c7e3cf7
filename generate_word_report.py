import argparse
import os
from typing import Dict, List, Tuple

import pandas as pd
from docx import Document
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn
from docx.shared import Pt, RGBColor


# -----------------------------
# 常量与列映射（基于现有模板）
# -----------------------------

# 注意：以下索引为零基索引（DataFrame iloc 列号）
COLUMN_MAP: Dict[str, int] = {
    # 标题层级
    "second_level": 2,   # 二级标题 -> C列
    "third_level": 3,    # 三级标题 -> D列
    # 文本描述
    "issue_desc": 4,     # 问题情形描述 -> E列
    "basis": 10,         # 认定依据 -> K列
    # 统计数据
    "quantity": 5,       # 全量问题数量 -> F列
    "violation_amount": 6,  # 全量查实违规违约医疗总金额 -> G列
    "fund": 7,           # 医保基金支付总金额 -> H列
    "resident": 8,       # 居民金额 -> I列
    "staff": 9,          # 职工金额 -> J列
    "other_fund": 12,    # 其他基金 -> M列
}


# -----------------------------
# 工具函数（函数式实现）
# -----------------------------

def is_empty(value: object) -> bool:
    if pd.isna(value):
        return True
    if isinstance(value, str) and value.strip() == "":
        return True
    return False


def is_valid_value(value: object) -> bool:
    if is_empty(value):
        return False
    try:
        num = round(float(value), 2)
        return num != 0
    except (ValueError, TypeError):
        return True


def format_decimal(value: object) -> str:
    if is_empty(value):
        return ""
    try:
        return f"{round(float(value), 2):.2f}"
    except (ValueError, TypeError):
        return str(value).strip()


def delete_existing_file(file_path: str) -> None:
    if os.path.exists(file_path):
        os.remove(file_path)


def read_excel_template(excel_path: str) -> Tuple[pd.DataFrame, str, int]:
    df = pd.read_excel(excel_path, sheet_name="Sheet1", header=None)

    # 医疗机构名称：A3 单元格，格式类似 "医疗机构名称：XXX"
    org_cell = df.iloc[2, 0]
    org_name = (
        str(org_cell).split("：")[-1].strip() if not is_empty(org_cell) else "未知机构"
    )

    # 查找“总计”所在行（首列）
    total_row_idx = None
    for i in range(len(df)):
        cell_value = df.iloc[i, 0]
        if not is_empty(cell_value) and str(cell_value).strip() == "总计":
            total_row_idx = i
            break
    if total_row_idx is None:
        raise ValueError("未找到包含‘总计’的行")

    return df, org_name, total_row_idx


def read_desc_paragraphs(sample_docx_path: str) -> List[str]:
    # 复用现有方案：读取示例 Word 的正文段落作为“医疗机构情况说明”
    doc = Document(sample_docx_path)
    paragraphs: List[str] = []
    for p in doc.paragraphs:
        t = p.text.strip()
        if t:
            paragraphs.append(t)
    return paragraphs


def apply_styles(doc: Document) -> None:
    # 1级标题
    h1 = doc.styles["Heading 1"]
    h1.font.name = "黑体"
    h1.font.size = Pt(22)
    h1.font.bold = True
    h1.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    h1.font.element.rPr.rFonts.set(qn("w:eastAsia"), "黑体")
    h1.font.color.rgb = RGBColor(0, 0, 0)
    h1.paragraph_format.space_before = Pt(6)
    h1.paragraph_format.space_after = Pt(6)

    # 2级标题
    h2 = doc.styles["Heading 2"]
    h2.font.name = "黑体"
    h2.font.size = Pt(16)
    h2.font.bold = True
    h2.font.element.rPr.rFonts.set(qn("w:eastAsia"), "黑体")
    h2.font.color.rgb = RGBColor(0, 0, 0)
    h2.paragraph_format.space_before = Pt(6)
    h2.paragraph_format.space_after = Pt(6)

    # 3级标题
    h3 = doc.styles["Heading 3"]
    h3.font.name = "黑体"
    h3.font.size = Pt(16)
    h3.font.bold = False
    h3.font.element.rPr.rFonts.set(qn("w:eastAsia"), "黑体")
    h3.font.color.rgb = RGBColor(0, 0, 0)
    h3.paragraph_format.space_before = Pt(6)
    h3.paragraph_format.space_after = Pt(6)

    # 正文
    normal = doc.styles["Normal"]
    normal.font.name = "方正仿宋_GB2312"
    normal.font.size = Pt(16)
    normal.font.element.rPr.rFonts.set(qn("w:eastAsia"), "方正仿宋_GB2312")
    normal.paragraph_format.space_before = Pt(6)
    normal.paragraph_format.space_after = Pt(6)


def build_total_paragraph(doc: Document, df: pd.DataFrame, total_row_idx: int) -> None:
    total_all = format_decimal(df.iloc[total_row_idx, COLUMN_MAP["quantity"]])
    total_allfund = format_decimal(df.iloc[total_row_idx, COLUMN_MAP["violation_amount"]])
    total_fund = format_decimal(df.iloc[total_row_idx, COLUMN_MAP["fund"]])
    total_resident = format_decimal(df.iloc[total_row_idx, COLUMN_MAP["resident"]])
    total_staff = format_decimal(df.iloc[total_row_idx, COLUMN_MAP["staff"]])
    total_qt = format_decimal(df.iloc[total_row_idx, COLUMN_MAP["other_fund"]])

    parts: List[str] = []
    if is_valid_value(total_fund):
        parts.append(f"违规违约医保基金支付总金额{total_fund}元")
    if is_valid_value(total_resident):
        parts.append(f"违规违约医保基金支付居民金额：{total_resident}元")
    if is_valid_value(total_staff):
        parts.append(f"违规违约医保基金支付职工金额：{total_staff}元")
    if is_valid_value(total_qt):
        parts.append(f"违规违约医保基金支付其他金额：{total_qt}元")
    if is_valid_value(total_all):
        parts.append(f"全量问题总数量{total_all}")
    if is_valid_value(total_allfund):
        parts.append(f"全量查实违规违约医疗总金额{total_allfund}元")

    if parts:
        p = doc.add_paragraph()
        p.add_run("该医院" + "，".join(parts) + "。")
        p.paragraph_format.line_spacing = 1.5
        p.paragraph_format.first_line_indent = Pt(24)


def add_issue_block(doc: Document, df: pd.DataFrame, row_idx: int, state: Dict[str, Tuple[int, int]]) -> Dict[str, Tuple[int, int]]:
    # 二级标题
    second_val = df.iloc[row_idx, COLUMN_MAP["second_level"]]
    if not is_empty(second_val):
        second_text = str(second_val).strip()
        if second_text not in state:
            number = len(state) + 1
            doc.add_heading(f"{number}. {second_text}", level=2)
            state[second_text] = (number, 1)
        current_num, third_counter = state.get(second_text, (0, 0))
    else:
        # 若缺失，则复用上文的 second_text（通过 state 保持）
        # 不更新序号
        # 尝试获取最近的二级标题上下文
        if len(state) > 0:
            # 取最后一个键
            last_key = list(state.keys())[-1]
            current_num, third_counter = state[last_key]
            second_text = last_key
        else:
            second_text = ""
            current_num, third_counter = (0, 0)

    # 三级标题
    third_val = df.iloc[row_idx, COLUMN_MAP["third_level"]]
    if not is_empty(third_val) and second_text in state:
        doc.add_heading(f"{current_num}.{third_counter} {str(third_val).strip()}", level=3)
        state[second_text] = (current_num, third_counter + 1)

    # 问题描述
    desc_val = df.iloc[row_idx, COLUMN_MAP["issue_desc"]]
    if not is_empty(desc_val):
        p = doc.add_paragraph()
        p.add_run("　　" + str(desc_val).strip())
        p.paragraph_format.line_spacing = 1.5

    # 认定依据
    basis_val = df.iloc[row_idx, COLUMN_MAP["basis"]]
    if not is_empty(basis_val):
        p = doc.add_paragraph()
        p.add_run("　　" + str(basis_val).strip())
        p.paragraph_format.line_spacing = 1.5

    # 金额类信息
    fund = format_decimal(df.iloc[row_idx, COLUMN_MAP["fund"]])
    resident = format_decimal(df.iloc[row_idx, COLUMN_MAP["resident"]])
    staff = format_decimal(df.iloc[row_idx, COLUMN_MAP["staff"]])
    other_fund = format_decimal(df.iloc[row_idx, COLUMN_MAP["other_fund"]])
    quantity = format_decimal(df.iloc[row_idx, COLUMN_MAP["quantity"]])
    violation_amount = format_decimal(df.iloc[row_idx, COLUMN_MAP["violation_amount"]])

    parts: List[str] = []
    if is_valid_value(fund):
        parts.append(f"医保基金支付总金额{fund}元")
    if is_valid_value(resident):
        parts.append(f"医保基金支付居民金额：{resident}元")
    if is_valid_value(staff):
        parts.append(f"医保基金支付职工金额：{staff}元")
    if is_valid_value(other_fund):
        parts.append(f"其他基金支付金额：{other_fund}元")
    if is_valid_value(quantity):
        parts.append(f"问题总数量{quantity}")
    if is_valid_value(violation_amount):
        parts.append(f"查实违规违约医疗总金额{violation_amount}元")

    if parts:
        p = doc.add_paragraph()
        p.add_run("　　" + "，".join(parts) + "。")
        p.paragraph_format.line_spacing = 1.5

    return state


def generate_word_from_excel(
    excel_path: str,
    sample_docx_path: str,
    output_path: str,
    report_title: str = "飞行检查情况反馈报告",
    start_row_index: int = 5,
) -> None:
    delete_existing_file(output_path)

    df, org_name, total_row_idx = read_excel_template(excel_path)
    desc_paragraphs = read_desc_paragraphs(sample_docx_path)

    doc = Document()
    apply_styles(doc)

    # 标题与说明
    doc.add_heading(org_name, level=1)
    doc.add_heading(report_title, level=1)

    if desc_paragraphs:
        for t in desc_paragraphs:
            p = doc.add_paragraph()
            p.add_run("　　" + t)
            p.paragraph_format.line_spacing = 1.5
            p.paragraph_format.first_line_indent = Pt(24)
    else:
        p = doc.add_paragraph("　　无医疗机构情况说明。")
        p.paragraph_format.first_line_indent = Pt(24)

    # 总计信息
    build_total_paragraph(doc, df, total_row_idx)

    # 明细区间（从可配置的起始行到“总计”上一行）
    start_row = start_row_index
    end_row = total_row_idx - 1
    if start_row > end_row:
        p = doc.add_paragraph("　　无具体项目数据。")
        p.paragraph_format.first_line_indent = Pt(24)
        doc.save(output_path)
        return

    second_state: Dict[str, Tuple[int, int]] = {}
    for r in range(start_row, end_row + 1):
        second_state = add_issue_block(doc, df, r, second_state)

    doc.save(output_path)


def build_arg_parser() -> argparse.ArgumentParser:
    parser = argparse.ArgumentParser(
        description="从Excel模板生成医疗机构检查报告 Word 文档"
    )
    parser.add_argument(
        "--excel",
        dest="excel",
        type=str,
        default="附件7-1.飞行检查情况反馈报告-医疗机构模板.xlsx",
        help="Excel 模板路径，默认读取当前目录模板文件",
    )
    parser.add_argument(
        "--sample",
        dest="sample",
        type=str,
        default="附件7-柳州市柳江区人民医院.docx",
        help="示例 Word，用作段落样式参考与说明来源",
    )
    parser.add_argument(
        "--output",
        dest="output",
        type=str,
        default="自动生成检查报告.docx",
        help="输出 Word 文件路径",
    )
    parser.add_argument(
        "--title",
        dest="title",
        type=str,
        default="飞行检查情况反馈报告",
        help="报告主标题，默认：飞行检查情况反馈报告",
    )
    parser.add_argument(
        "--start-row",
        dest="start_row",
        type=int,
        default=5,
        help="数据起始行（零基索引，默认第6行即5）",
    )
    return parser


def main() -> None:
    parser = build_arg_parser()
    args = parser.parse_args()
    generate_word_from_excel(
        excel_path=args.excel,
        sample_docx_path=args.sample,
        output_path=args.output,
        report_title=args.title,
        start_row_index=args.start_row,
    )


if __name__ == "__main__":
    main()

