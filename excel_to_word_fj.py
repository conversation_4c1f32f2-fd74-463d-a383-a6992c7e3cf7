import pandas as pd
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn  
import os

def is_valid_value(value):
    """判断值是否有效：非空且不等于0/0.0"""
    if is_empty(value):  
        return False
    try:
        num = round(float(value), 2)
        return num != 0  
    except (ValueError, TypeError):
        return True

def format_decimal(value):
    if is_empty(value):
        return ""
    try:
        return f"{round(float(value), 2):.2f}"
    except (ValueError, TypeError):
        return str(value).strip()    
def is_empty(value):
    """判断单元格内容是否为空"""
    if pd.isna(value):
        return True
    if isinstance(value, str) and str(value).strip() == "":
        return True
    return False

def delete_existing_file(file_path):
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            print(f"已删除现有文件：{file_path}")
        except Exception as e:
            print(f"删除文件时出错：{e}")
            raise

def read_excel_by_position(excel_path):
    df = pd.read_excel(excel_path, sheet_name="Sheet1", header=None)
    
    org_name_cell = df.iloc[2, 0]
    org_name = str(org_name_cell).split("：")[-1].strip() if not is_empty(org_name_cell) else "未知机构"
    
    total_row_idx = None
    for i in range(len(df)):
        cell_value = df.iloc[i, 0]
        if not is_empty(cell_value) and str(cell_value).strip() == "总计":
            total_row_idx = i
            break
    
    if total_row_idx is None:
        raise ValueError("未在D列找到包含'总计'的行")
    
    return df, org_name, total_row_idx

def read_doc_description(doc_path):
    """读取说明文档内容，保留原始段落结构（返回段落列表）"""
    doc = Document(doc_path)
    paragraphs = []
    for para in doc.paragraphs:
        text = para.text.strip()
        if text:  # 只保留非空段落
            paragraphs.append(text)
    return paragraphs  # 返回段落列表，而非合并字符串

def create_report(excel_path, desc_doc_path, output_path):
    delete_existing_file(output_path)
    
    df, org_name, total_row_idx = read_excel_by_position(excel_path)
    desc_paragraphs = read_doc_description(desc_doc_path)  # 获取段落列表
    
    doc = Document()

    # ======================
    # 样式设置
    # ======================

    # 1级标题样式
    title1_style = doc.styles['Heading 1']
    title1_style.font.name = '黑体'
    title1_style.font.size = Pt(22)  
    title1_style.font.bold = True
    title1_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    title1_style.font.element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
    title1_style.font.color.rgb = RGBColor(0, 0, 0)  
    title1_style.paragraph_format.space_before = Pt(6)  
    title1_style.paragraph_format.space_after = Pt(6)  

    # 2级标题样式
    title2_style = doc.styles['Heading 2']
    title2_style.font.name = '黑体'
    title2_style.font.size = Pt(16)  
    title2_style.font.bold = True
    title2_style.font.element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
    title2_style.font.color.rgb = RGBColor(0, 0, 0)  
    title2_style.paragraph_format.space_before = Pt(6)  
    title2_style.paragraph_format.space_after = Pt(6)  

    # 3级标题样式
    title3_style = doc.styles['Heading 3']
    title3_style.font.name = '黑体'
    title3_style.font.size = Pt(16)  
    title3_style.font.bold = False
    title3_style.font.element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
    title3_style.font.color.rgb = RGBColor(0, 0, 0)  
    title3_style.paragraph_format.space_before = Pt(6)  
    title3_style.paragraph_format.space_after = Pt(6)  

    # 正文样式
    normal_style = doc.styles['Normal']
    normal_style.font.name = '方正仿宋_GB2312'
    normal_style.font.size = Pt(16)  
    normal_style.font.element.rPr.rFonts.set(qn('w:eastAsia'), '方正仿宋_GB2312')
    normal_style.paragraph_format.space_before = Pt(6)  
    normal_style.paragraph_format.space_after = Pt(6)  


    # 1. 医疗机构名称（标题1）
    doc.add_heading(org_name, level=1)
    
    # 2. 检查报告（标题1）
    doc.add_heading("检查报告\n", level=1)
    
    # 3. 医疗机构情况说明（保留原段落结构）
    if desc_paragraphs:  # 如果有段落
        for para_text in desc_paragraphs:
            desc_para = doc.add_paragraph()
            desc_para.add_run("　　" + para_text)  # 每个段落前加2个字符缩进
            desc_para.paragraph_format.line_spacing = 1.5
    else:
        doc.add_paragraph("　　无医疗机构情况说明。")
    
    # 4. 总计信息
    total_all = df.iloc[total_row_idx, 5]    #全量问题总数量
    total_allfund = df.iloc[total_row_idx, 6]    #全量查实违规违约医疗总金额（元）
    total_fund = df.iloc[total_row_idx, 7]   #总额
    total_resident = df.iloc[total_row_idx, 8]     #居民
    total_staff = df.iloc[total_row_idx, 9]  #职工
    total_qt = df.iloc[total_row_idx, 12]  #其他基金

    total_all = format_decimal(total_all)
    total_allfund = format_decimal(total_allfund)
    total_fund = format_decimal(total_fund)
    total_resident = format_decimal(total_resident)
    total_staff = format_decimal(total_staff)
    total_qt = format_decimal(total_qt)

    total_parts = []
    if is_valid_value(total_fund):  
        total_parts.append(f"违规违约医保基金支付总金额{total_fund}元")
    if is_valid_value(total_resident):  
        total_parts.append(f"违规违约医保基金支付居民金额：{total_resident}元")
    if is_valid_value(total_staff):  
        total_parts.append(f"违规违约医保基金支付职工金额：{total_staff}元")
    if is_valid_value(total_qt):  
        total_parts.append(f"违规违约医保基金支付其他金额：{total_qt}元")
    if is_valid_value(total_all):  
        total_parts.append(f"全量问题总数量{total_all}")
    if is_valid_value(total_allfund):  
        total_parts.append(f"全量查实违规违约医疗总金额{total_allfund}元")
    
    if total_parts:
        total_para = doc.add_paragraph()
        total_para.add_run("该医院" + "，".join(total_parts) + "。")
        total_para.paragraph_format.line_spacing = 1.5
        total_para.paragraph_format.first_line_indent = Pt(24)  
    else:
        doc.add_paragraph("")
    
    # 5. 处理项目情况
    start_row = 5  # 第6行
    end_row = total_row_idx - 1  # 总计行上一行
    seen_second_level = {}  # 存储二级标题及其序号
    second_level_counter = 1  # 二级标题序号计数器
    
    if start_row > end_row:
        doc.add_paragraph("　　无具体项目数据")
        doc.save(output_path)
        return
    
    # 循环处理每一行数据
    for row_idx in range(start_row, end_row + 1):
        # 二级标题：C列(2)
        second_level_title = df.iloc[row_idx, 2]
        if not is_empty(second_level_title):
            title_str = str(second_level_title).strip()
            
            # 处理二级标题（首次出现）
            if title_str not in seen_second_level:
                numbered_title = f"{second_level_counter}. {title_str}"
                doc.add_heading(numbered_title, level=2)
                seen_second_level[title_str] = (second_level_counter, 1)
                second_level_counter += 1
            
            current_second_num, current_third_counter = seen_second_level.get(title_str, (0, 0))
        
        # 三级标题：D列(3)
        third_level_title = df.iloc[row_idx, 3]
        if not is_empty(third_level_title) and title_str in seen_second_level:
            numbered_third_title = f"{current_second_num}.{current_third_counter} {str(third_level_title).strip()}"
            doc.add_heading(numbered_third_title, level=3)
            seen_second_level[title_str] = (current_second_num, current_third_counter + 1)

        # 问题情形描述：E列(4)
        desc = df.iloc[row_idx, 4]
        if not is_empty(desc):
            desc_para = doc.add_paragraph()
            desc_para.add_run("　　" + str(desc).strip())
            desc_para.paragraph_format.line_spacing = 1.5

        # 认定依据：K列(10)
        basis = df.iloc[row_idx, 10]
        if not is_empty(basis):
            basis_para = doc.add_paragraph()
            basis_para.add_run("　　" + str(basis).strip())
            basis_para.paragraph_format.line_spacing = 1.5
        
        # 涉及金额信息（保留2位小数并过滤0值）
        fund = df.iloc[row_idx, 7]
        resident = df.iloc[row_idx, 8]
        staff = df.iloc[row_idx, 9]
        quantity = df.iloc[row_idx, 5]
        violation_amount = df.iloc[row_idx, 6]
        grzh = df.iloc[row_idx, 12]
        
        # 格式化数值保留2位小数

        
        fund = format_decimal(fund)
        resident = format_decimal(resident)
        staff = format_decimal(staff)
        violation_amount = format_decimal(violation_amount)
        quantity = format_decimal(quantity)
        grzh = format_decimal(grzh)
 
        
        amount_parts = []
        if is_valid_value(fund):
            amount_parts.append(f"医保基金支付总金额{fund}元")
        if is_valid_value(resident):
            amount_parts.append(f"医保基金支付居民金额：{resident}元")
        if is_valid_value(staff):
            amount_parts.append(f"医保基金支付职工金额：{staff}元")
        if is_valid_value(grzh):
            amount_parts.append(f"其他基金支付金额：{grzh}元")
        if is_valid_value(quantity):
            amount_parts.append(f"问题总数量{quantity}")
        if is_valid_value(violation_amount):
            amount_parts.append(f"查实违规违约医疗总金额{violation_amount}元")
        
        if amount_parts:
            amount_para = doc.add_paragraph()
            amount_para.add_run("　　" + "，".join(amount_parts) + "。")
            amount_para.paragraph_format.line_spacing = 1.5
    
    doc.save(output_path)
    print(f"报告已生成：{output_path}")
 

if __name__ == "__main__":
    # 定义基础路径（目标目录）    
    base_dir = r"D:\壹慧聆康\规则文件\海南\筛查结果\违规数据汇总报表"
    
    # 拼接各文件完整路径
    excel_path = os.path.join(base_dir, "三亚市人民医院汇总报表.xlsx")
    desc_doc_path = os.path.join(base_dir, "简介.docx")
    output_path = os.path.join(base_dir, "三亚市人民医院检查报告.docx")
    
    create_report(excel_path, desc_doc_path, output_path)
