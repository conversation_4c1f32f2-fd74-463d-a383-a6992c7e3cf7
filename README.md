# Excel转Word飞行检查报告生成器

## 功能描述

这个Python脚本可以自动从Excel模板文件读取飞行检查数据，并生成格式化的Word文档报告。

## 文件说明

- `excel_to_word_report.py` - 主要的转换脚本
- `附件7-1.飞行检查情况反馈报告-医疗机构模板.xlsx` - Excel数据模板
- `附件7-柳州市柳江区人民医院.docx` - Word文档格式参考
- `excel_to_word_fj.py` - 原有的参考脚本

## 依赖库

确保安装以下Python库：

```bash
pip install pandas python-docx openpyxl
```

## 使用方法

### 基本用法

```bash
python excel_to_word_report.py
```

这将使用默认的Excel文件生成Word报告。

### 指定文件路径

```bash
python excel_to_word_report.py --excel "你的Excel文件.xlsx" --output "输出报告.docx"
```

### 命令行参数

- `--excel` 或 `-e`: 指定Excel模板文件路径（默认：附件7-1.飞行检查情况反馈报告-医疗机构模板.xlsx）
- `--output` 或 `-o`: 指定输出Word文档路径（默认：生成的飞行检查报告.docx）
- `--help` 或 `-h`: 显示帮助信息

## Excel文件格式要求

### 表1 - 医院基本信息
包含医院的基本信息，如：
- 被检单位名称
- 机构代码
- 医保机构编码
- 单位类别/性质/等级
- 法定代表人信息
- 检查时间和地点
- 检查组信息等

### 表2 - 问题详细数据
包含具体的问题数据，如：
- 序号
- 机构名称
- 问题类别（一级指标、二级指标）
- 科室
- 问题项目和编码
- 问题情形描述
- 问题数量
- 违规医药费用总额
- 违规使用医保基金
- 认定依据
- 处理建议等

## 生成的Word文档结构

1. **飞行检查报告标题**
2. **医院基本信息**
   - 被检单位名称、代码等
   - 检查时间、地点
   - 检查组信息
3. **检查总体情况**
   - 医疗总收入
   - 医保基金收入
   - 报销比例
4. **违法违规使用医保基金的问题**
   - 按问题类别分组
   - 详细的问题描述和认定依据
   - 涉及金额信息
5. **检查发现涉及其他部门职能的相关问题**
   - 涉及卫生健康部门职能问题
   - 涉及药品监管部门职能问题
6. **飞行检查组意见**
7. **签名部分**

## 特性

- ✅ 自动读取Excel模板数据
- ✅ 按问题类别自动分组和编号
- ✅ 格式化数值显示（保留2位小数）
- ✅ 过滤空值和无效数据
- ✅ 自动计算总计信息
- ✅ 完全按照原Word文档样式格式化（Times New Roman字体，正确的字号和缩进）
- ✅ 命令行参数支持
- ✅ 详细的错误处理和提示
- ✅ 自动处理涉及其他部门职能的问题分类

## 注意事项

1. 确保Excel文件包含"表1"和"表2"工作表
2. Excel文件的列结构应与模板保持一致
3. 生成的Word文档会覆盖同名的现有文件
4. 脚本会自动过滤空值和0值数据

## 错误处理

脚本包含完善的错误处理机制：
- 文件不存在检查
- Excel格式验证
- 数据完整性检查
- 详细的错误提示信息

## 示例输出

```
正在读取Excel文件：附件7-1.飞行检查情况反馈报告-医疗机构模板.xlsx
报告已生成：完全按原样式生成的飞行检查报告.docx

总计信息：
问题总数量：244982
违规医药费用总额：4129972.16元
违规使用医保基金：4129972.16元
✓ 报告生成成功：完全按原样式生成的飞行检查报告.docx
```

## 样式说明

生成的Word文档完全按照 `附件7-柳州市柳江区人民医院.docx` 的样式格式：

- **主标题**（飞行检查报告）：Times New Roman，22磅，居中对齐
- **二级标题**（检查总体情况等）：Times New Roman，16磅，不加粗，首行缩进32磅
- **三级标题**（问题分类等）：Times New Roman，16磅，加粗，首行缩进32磅
- **正文内容**：Times New Roman，16磅，不加粗，首行缩进32磅
